-- System Configuration Management
-- 系统配置管理表，用于存储各种系统级配置项

CREATE TABLE IF NOT EXISTS system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) NOT NULL DEFAULT 'string',
    description TEXT,
    category VARCHAR(50) NOT NULL DEFAULT 'general',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_by INTEGER REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_system_configs_config_key ON system_configs(config_key);
CREATE INDEX idx_system_configs_category ON system_configs(category);
CREATE INDEX idx_system_configs_is_active ON system_configs(is_active);

-- 添加约束
ALTER TABLE system_configs 
ADD CONSTRAINT chk_config_type 
CHECK (config_type IN ('string', 'boolean', 'integer', 'decimal', 'json'));

-- 插入默认配置项
INSERT INTO system_configs (config_key, config_value, config_type, description, category) VALUES
-- 计划分配模式配置
('plan_assignment_mode', 'skill_group', 'string', '计划任务分配模式：skill_group（分配到技能组）或 machine（分配到设备）', 'planning'),
('plan_assignment_mode_enabled', 'true', 'boolean', '是否启用计划分配模式切换功能', 'planning'),

-- 其他系统配置
('system_name', 'MES生产管理系统', 'string', '系统名称', 'general'),
('default_time_zone', 'Asia/Shanghai', 'string', '默认时区', 'general'),
('min_task_duration_minutes', '30', 'integer', '最小任务持续时间（分钟）', 'planning'),
('max_concurrent_tasks_per_machine', '1', 'integer', '每台设备最大并发任务数', 'planning'),

-- 界面配置
('default_language', 'zh-CN', 'string', '默认界面语言', 'ui'),
('enable_gantt_chart', 'true', 'boolean', '是否启用甘特图功能', 'ui'),
('enable_barcode_scanning', 'true', 'boolean', '是否启用条码扫描功能', 'ui')

ON CONFLICT (config_key) DO NOTHING;

-- 创建更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_system_configs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_system_configs_updated_at
    BEFORE UPDATE ON system_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_system_configs_updated_at();
