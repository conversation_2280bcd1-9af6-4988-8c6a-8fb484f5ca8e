-- Add machine support to plan_tasks table
-- 为计划任务表添加设备支持，允许任务分配到具体设备

-- 添加machine_id字段（可选）
ALTER TABLE plan_tasks 
ADD COLUMN machine_id INTEGER REFERENCES machines(id);

-- 创建索引以提高查询性能
CREATE INDEX idx_plan_tasks_machine_id ON plan_tasks(machine_id);

-- 注意：数据一致性检查（machine必须属于指定的skill_group）在应用层进行验证
-- PostgreSQL不支持在CHECK约束中使用子查询，所以我们在应用代码中确保这个约束

-- 添加注释说明字段用途
COMMENT ON COLUMN plan_tasks.machine_id IS '分配的具体设备ID（可选）。如果为空，则任务分配给整个技能组';
COMMENT ON COLUMN plan_tasks.skill_group_id IS '分配的技能组ID。即使指定了具体设备，也需要指定技能组以确保一致性';

-- 更新现有数据的注释
COMMENT ON TABLE plan_tasks IS '生产计划任务表。支持两种分配模式：1) 分配给技能组（machine_id为空）2) 分配给具体设备（machine_id有值）';
