use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SystemConfig {
    pub id: i32,
    pub config_key: String,
    pub config_value: String,
    pub config_type: String,
    pub description: Option<String>,
    pub category: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub updated_by: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateSystemConfigRequest {
    pub config_key: String,
    pub config_value: String,
    pub config_type: String,
    pub description: Option<String>,
    pub category: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateSystemConfigRequest {
    pub config_value: Option<String>,
    pub config_type: Option<String>,
    pub description: Option<String>,
    pub category: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SystemConfigQuery {
    pub category: Option<String>,
    pub config_key: Option<String>,
    pub is_active: Option<bool>,
}

// 计划分配模式枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PlanAssignmentMode {
    #[serde(rename = "skill_group")]
    SkillGroup,
    #[serde(rename = "machine")]
    Machine,
}

impl PlanAssignmentMode {
    pub fn from_string(s: &str) -> Result<Self, String> {
        match s {
            "skill_group" => Ok(PlanAssignmentMode::SkillGroup),
            "machine" => Ok(PlanAssignmentMode::Machine),
            _ => Err(format!("Invalid plan assignment mode: {}", s)),
        }
    }

    pub fn to_string(&self) -> String {
        match self {
            PlanAssignmentMode::SkillGroup => "skill_group".to_string(),
            PlanAssignmentMode::Machine => "machine".to_string(),
        }
    }
}

// 系统配置值的类型化访问
#[derive(Debug, Serialize, Deserialize)]
pub struct TypedConfigValue {
    pub key: String,
    pub value: ConfigValue,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ConfigValue {
    String(String),
    Boolean(bool),
    Integer(i64),
    Decimal(f64),
    Json(serde_json::Value),
}

impl ConfigValue {
    pub fn from_config(config: &SystemConfig) -> Result<Self, String> {
        match config.config_type.as_str() {
            "string" => Ok(ConfigValue::String(config.config_value.clone())),
            "boolean" => {
                let value = config.config_value.parse::<bool>()
                    .map_err(|_| format!("Invalid boolean value: {}", config.config_value))?;
                Ok(ConfigValue::Boolean(value))
            },
            "integer" => {
                let value = config.config_value.parse::<i64>()
                    .map_err(|_| format!("Invalid integer value: {}", config.config_value))?;
                Ok(ConfigValue::Integer(value))
            },
            "decimal" => {
                let value = config.config_value.parse::<f64>()
                    .map_err(|_| format!("Invalid decimal value: {}", config.config_value))?;
                Ok(ConfigValue::Decimal(value))
            },
            "json" => {
                let value = serde_json::from_str(&config.config_value)
                    .map_err(|_| format!("Invalid JSON value: {}", config.config_value))?;
                Ok(ConfigValue::Json(value))
            },
            _ => Err(format!("Unknown config type: {}", config.config_type)),
        }
    }

    pub fn to_string(&self) -> String {
        match self {
            ConfigValue::String(s) => s.clone(),
            ConfigValue::Boolean(b) => b.to_string(),
            ConfigValue::Integer(i) => i.to_string(),
            ConfigValue::Decimal(d) => d.to_string(),
            ConfigValue::Json(j) => serde_json::to_string(j).unwrap_or_default(),
        }
    }
}

// 常用配置键常量
pub const CONFIG_PLAN_ASSIGNMENT_MODE: &str = "plan_assignment_mode";
pub const CONFIG_PLAN_ASSIGNMENT_MODE_ENABLED: &str = "plan_assignment_mode_enabled";
pub const CONFIG_MIN_TASK_DURATION_MINUTES: &str = "min_task_duration_minutes";
pub const CONFIG_MAX_CONCURRENT_TASKS_PER_MACHINE: &str = "max_concurrent_tasks_per_machine";
pub const CONFIG_DEFAULT_LANGUAGE: &str = "default_language";
pub const CONFIG_ENABLE_GANTT_CHART: &str = "enable_gantt_chart";
pub const CONFIG_ENABLE_BARCODE_SCANNING: &str = "enable_barcode_scanning";

// 配置类别常量
pub const CATEGORY_PLANNING: &str = "planning";
pub const CATEGORY_GENERAL: &str = "general";
pub const CATEGORY_UI: &str = "ui";
