use crate::models::part::{
    CreatePartRequest, Part, PartSearchQuery, PartSearchResult, UpdatePartRequest,
};
use sqlx::PgPool;

pub struct PartService {
    pool: PgPool,
}

impl PartService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_part(&self, request: CreatePartRequest) -> Result<Part, String> {
        // Check if part with same number and version already exists
        let existing_part = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM parts WHERE part_number = $1 AND version = $2)",
            request.part_number,
            request.version
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if existing_part {
            return Err("Part with this number and version already exists".to_string());
        }

        let part = sqlx::query_as!(
            Part,
            "INSERT INTO parts (part_number, part_name, version, specifications)
             VALUES ($1, $2, $3, $4)
             RETURNING id, part_number, part_name, version, specifications",
            request.part_number,
            request.part_name,
            request.version,
            request.specifications
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(part)
    }

    pub async fn get_all_parts(
        &self,
        limit: Option<i64>,
        offset: Option<i64>,
    ) -> Result<PartSearchResult, String> {
        let limit = limit.unwrap_or(50).min(100); // Max 100 items per page
        let offset = offset.unwrap_or(0);

        let parts = sqlx::query_as!(
            Part,
            "SELECT id, part_number, part_name, version, specifications 
             FROM parts 
             ORDER BY part_number, version 
             LIMIT $1 OFFSET $2",
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let total_count = sqlx::query_scalar!("SELECT COUNT(*) FROM parts")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?
            .unwrap_or(0);

        Ok(PartSearchResult {
            parts,
            total_count,
            limit,
            offset,
        })
    }

    pub async fn search_parts(&self, query: PartSearchQuery) -> Result<PartSearchResult, String> {
        let limit = query.limit.unwrap_or(50).min(100);
        let offset = query.offset.unwrap_or(0);

        // If project_id is specified, search within project BOM
        if let Some(project_id) = query.project_id {
            return self.search_parts_in_project(project_id, &query, limit, offset).await;
        }

        // Handle different search combinations with static queries
        let (parts, total_count) = match (&query.part_number, &query.part_name, &query.version) {
            (Some(part_number), None, None) => {
                let pattern = format!("%{}%", part_number);
                let parts = sqlx::query_as!(
                    Part,
                    "SELECT id, part_number, part_name, version, specifications
                     FROM parts
                     WHERE part_number ILIKE $1
                     ORDER BY part_number, version
                     LIMIT $2 OFFSET $3",
                    pattern,
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let count = sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM parts WHERE part_number ILIKE $1",
                    pattern
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

                (parts, count)
            }
            (None, Some(part_name), None) => {
                let pattern = format!("%{}%", part_name);
                let parts = sqlx::query_as!(
                    Part,
                    "SELECT id, part_number, part_name, version, specifications
                     FROM parts
                     WHERE part_name ILIKE $1
                     ORDER BY part_number, version
                     LIMIT $2 OFFSET $3",
                    pattern,
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let count = sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM parts WHERE part_name ILIKE $1",
                    pattern
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

                (parts, count)
            }
            (None, None, Some(version)) => {
                let parts = sqlx::query_as!(
                    Part,
                    "SELECT id, part_number, part_name, version, specifications
                     FROM parts
                     WHERE version = $1
                     ORDER BY part_number, version
                     LIMIT $2 OFFSET $3",
                    version,
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let count =
                    sqlx::query_scalar!("SELECT COUNT(*) FROM parts WHERE version = $1", version)
                        .fetch_one(&self.pool)
                        .await
                        .map_err(|e| format!("Database error: {}", e))?
                        .unwrap_or(0);

                (parts, count)
            }
            (Some(part_number), Some(part_name), None) => {
                let part_number_pattern = format!("%{}%", part_number);
                let part_name_pattern = format!("%{}%", part_name);
                let parts = sqlx::query_as!(
                    Part,
                    "SELECT id, part_number, part_name, version, specifications
                     FROM parts
                     WHERE part_number ILIKE $1 AND part_name ILIKE $2
                     ORDER BY part_number, version
                     LIMIT $3 OFFSET $4",
                    part_number_pattern,
                    part_name_pattern,
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let count = sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM parts WHERE part_number ILIKE $1 AND part_name ILIKE $2",
                    part_number_pattern,
                    part_name_pattern
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

                (parts, count)
            }
            _ => {
                // Default case: return all parts
                let parts = sqlx::query_as!(
                    Part,
                    "SELECT id, part_number, part_name, version, specifications
                     FROM parts
                     ORDER BY part_number, version
                     LIMIT $1 OFFSET $2",
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let count = sqlx::query_scalar!("SELECT COUNT(*) FROM parts")
                    .fetch_one(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))?
                    .unwrap_or(0);

                (parts, count)
            }
        };

        Ok(PartSearchResult {
            parts,
            total_count,
            limit,
            offset,
        })
    }

    pub async fn get_part_by_id(&self, part_id: i32) -> Result<Option<Part>, String> {
        let part = sqlx::query_as!(
            Part,
            "SELECT id, part_number, part_name, version, specifications 
             FROM parts WHERE id = $1",
            part_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(part)
    }

    pub async fn update_part(
        &self,
        part_id: i32,
        request: UpdatePartRequest,
    ) -> Result<Option<Part>, String> {
        // Check if part exists
        let part_exists =
            sqlx::query_scalar!("SELECT EXISTS(SELECT 1 FROM parts WHERE id = $1)", part_id)
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(false);

        if !part_exists {
            return Ok(None);
        }

        // Get current values
        let current = sqlx::query_as!(
            Part,
            "SELECT id, part_number, part_name, version, specifications FROM parts WHERE id = $1",
            part_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let part = sqlx::query_as!(
            Part,
            "UPDATE parts SET part_name = $1, specifications = $2
             WHERE id = $3
             RETURNING id, part_number, part_name, version, specifications",
            request.part_name.or(current.part_name),
            request.specifications.or(current.specifications),
            part_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(Some(part))
    }

    pub async fn delete_part(&self, part_id: i32) -> Result<bool, String> {
        // Check if part is used in any BOM
        let is_used = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM project_boms WHERE part_id = $1)",
            part_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?
        .unwrap_or(false);

        if is_used {
            return Err("Cannot delete part that is used in project BOMs".to_string());
        }

        let result = sqlx::query!("DELETE FROM parts WHERE id = $1", part_id)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    async fn search_parts_in_project(
        &self,
        project_id: i32,
        query: &PartSearchQuery,
        limit: i64,
        offset: i64,
    ) -> Result<PartSearchResult, String> {
        // Build base query for parts in project
        let mut where_conditions = vec!["pb.project_id = $1".to_string()];
        let mut params: Vec<Box<dyn sqlx::Encode<sqlx::Postgres> + Send + Sync>> = vec![Box::new(project_id)];
        let mut param_index = 2;

        // Add search conditions
        if let Some(part_number) = &query.part_number {
            where_conditions.push(format!("p.part_number ILIKE ${}", param_index));
            params.push(Box::new(format!("%{}%", part_number)));
            param_index += 1;
        }

        if let Some(part_name) = &query.part_name {
            where_conditions.push(format!("p.part_name ILIKE ${}", param_index));
            params.push(Box::new(format!("%{}%", part_name)));
            param_index += 1;
        }

        if let Some(version) = &query.version {
            where_conditions.push(format!("p.version ILIKE ${}", param_index));
            params.push(Box::new(format!("%{}%", version)));
            // param_index += 1; // 最后一个参数，不需要递增
        }

        let _where_clause = where_conditions.join(" AND ");

        // Use static queries for different parameter combinations
        let (parts, total_count) = match (query.part_number.as_ref(), query.part_name.as_ref(), query.version.as_ref()) {
            (Some(part_number), None, None) => {
                let pattern = format!("%{}%", part_number);
                let parts = sqlx::query_as!(
                    Part,
                    "SELECT DISTINCT p.id, p.part_number, p.part_name, p.version, p.specifications
                     FROM parts p
                     JOIN project_boms pb ON p.id = pb.part_id
                     WHERE pb.project_id = $1 AND p.part_number ILIKE $2
                     ORDER BY p.part_number, p.version
                     LIMIT $3 OFFSET $4",
                    project_id,
                    pattern,
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let count = sqlx::query_scalar!(
                    "SELECT COUNT(DISTINCT p.id)
                     FROM parts p
                     JOIN project_boms pb ON p.id = pb.part_id
                     WHERE pb.project_id = $1 AND p.part_number ILIKE $2",
                    project_id,
                    pattern
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

                (parts, count)
            }
            _ => {
                // Default case: no search filters, just return all parts in project
                let parts = sqlx::query_as!(
                    Part,
                    "SELECT DISTINCT p.id, p.part_number, p.part_name, p.version, p.specifications
                     FROM parts p
                     JOIN project_boms pb ON p.id = pb.part_id
                     WHERE pb.project_id = $1
                     ORDER BY p.part_number, p.version
                     LIMIT $2 OFFSET $3",
                    project_id,
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                let count = sqlx::query_scalar!(
                    "SELECT COUNT(DISTINCT p.id)
                     FROM parts p
                     JOIN project_boms pb ON p.id = pb.part_id
                     WHERE pb.project_id = $1",
                    project_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0);

                (parts, count)
            }
        };

        Ok(PartSearchResult {
            parts,
            total_count,
            limit,
            offset,
        })
    }
}
