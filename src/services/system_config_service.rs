use crate::models::system_config::{
    SystemConfig, CreateSystemConfigRequest, UpdateSystemConfigRequest, SystemConfigQuery,
    PlanAssignmentMode, TypedConfigValue, ConfigValue,
    CONFIG_PLAN_ASSIGNMENT_MODE, CONFIG_PLAN_ASSIGNMENT_MODE_ENABLED,
};
use sqlx::PgPool;

pub struct SystemConfigService {
    pool: PgPool,
}

impl SystemConfigService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    // 获取所有配置
    pub async fn get_all_configs(&self, query: Option<SystemConfigQuery>) -> Result<Vec<SystemConfig>, String> {
        let mut sql = "SELECT id, config_key, config_value, config_type, description, category, is_active, created_at, updated_at, updated_by FROM system_configs WHERE 1=1".to_string();
        let mut conditions = Vec::new();

        if let Some(q) = query {
            if let Some(category) = q.category {
                conditions.push(format!("AND category = '{}'", category));
            }
            if let Some(config_key) = q.config_key {
                conditions.push(format!("AND config_key = '{}'", config_key));
            }
            if let Some(is_active) = q.is_active {
                conditions.push(format!("AND is_active = {}", is_active));
            }
        }

        sql.push_str(&conditions.join(" "));
        sql.push_str(" ORDER BY category, config_key");

        let configs = sqlx::query_as::<_, SystemConfig>(&sql)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(configs)
    }

    // 根据键获取配置
    pub async fn get_config_by_key(&self, config_key: &str) -> Result<Option<SystemConfig>, String> {
        let config = sqlx::query_as!(
            SystemConfig,
            "SELECT id, config_key, config_value, config_type, description, category, is_active, created_at, updated_at, updated_by
             FROM system_configs 
             WHERE config_key = $1 AND is_active = true",
            config_key
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(config)
    }

    // 获取类型化的配置值
    pub async fn get_typed_config(&self, config_key: &str) -> Result<Option<TypedConfigValue>, String> {
        if let Some(config) = self.get_config_by_key(config_key).await? {
            let value = ConfigValue::from_config(&config)?;
            Ok(Some(TypedConfigValue {
                key: config.config_key,
                value,
                description: config.description,
            }))
        } else {
            Ok(None)
        }
    }

    // 获取计划分配模式
    pub async fn get_plan_assignment_mode(&self) -> Result<PlanAssignmentMode, String> {
        if let Some(config) = self.get_config_by_key(CONFIG_PLAN_ASSIGNMENT_MODE).await? {
            PlanAssignmentMode::from_string(&config.config_value)
        } else {
            // 默认返回技能组模式
            Ok(PlanAssignmentMode::SkillGroup)
        }
    }

    // 检查计划分配模式切换是否启用
    pub async fn is_plan_assignment_mode_enabled(&self) -> Result<bool, String> {
        if let Some(config) = self.get_config_by_key(CONFIG_PLAN_ASSIGNMENT_MODE_ENABLED).await? {
            config.config_value.parse::<bool>()
                .map_err(|_| "Invalid boolean value for plan_assignment_mode_enabled".to_string())
        } else {
            // 默认启用
            Ok(true)
        }
    }

    // 创建配置
    pub async fn create_config(&self, request: CreateSystemConfigRequest, user_id: i32) -> Result<SystemConfig, String> {
        // 检查配置键是否已存在
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM system_configs WHERE config_key = $1)",
            request.config_key
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if exists.unwrap_or(false) {
            return Err("Configuration key already exists".to_string());
        }

        let config = sqlx::query_as!(
            SystemConfig,
            "INSERT INTO system_configs (config_key, config_value, config_type, description, category, updated_by)
             VALUES ($1, $2, $3, $4, $5, $6)
             RETURNING id, config_key, config_value, config_type, description, category, is_active, created_at, updated_at, updated_by",
            request.config_key,
            request.config_value,
            request.config_type,
            request.description,
            request.category,
            user_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(config)
    }

    // 更新配置
    pub async fn update_config(&self, config_key: &str, request: UpdateSystemConfigRequest, user_id: i32) -> Result<SystemConfig, String> {
        // 检查配置是否存在
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM system_configs WHERE config_key = $1)",
            config_key
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if !exists.unwrap_or(false) {
            return Err("Configuration not found".to_string());
        }

        // 构建动态更新查询
        let mut set_clauses = vec!["updated_by = $1".to_string()];
        let mut param_count = 2;

        if request.config_value.is_some() {
            set_clauses.push(format!("config_value = ${}", param_count));
            param_count += 1;
        }
        if request.config_type.is_some() {
            set_clauses.push(format!("config_type = ${}", param_count));
            param_count += 1;
        }
        if request.description.is_some() {
            set_clauses.push(format!("description = ${}", param_count));
            param_count += 1;
        }
        if request.category.is_some() {
            set_clauses.push(format!("category = ${}", param_count));
            param_count += 1;
        }
        if request.is_active.is_some() {
            set_clauses.push(format!("is_active = ${}", param_count));
        }

        let sql = format!(
            "UPDATE system_configs SET {} WHERE config_key = ${}
             RETURNING id, config_key, config_value, config_type, description, category, is_active, created_at, updated_at, updated_by",
            set_clauses.join(", "),
            param_count
        );

        let mut query = sqlx::query_as::<_, SystemConfig>(&sql)
            .bind(user_id)
            .bind(config_key);

        if let Some(value) = request.config_value {
            query = query.bind(value);
        }
        if let Some(config_type) = request.config_type {
            query = query.bind(config_type);
        }
        if let Some(description) = request.description {
            query = query.bind(description);
        }
        if let Some(category) = request.category {
            query = query.bind(category);
        }
        if let Some(is_active) = request.is_active {
            query = query.bind(is_active);
        }

        let config = query
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(config)
    }

    // 设置计划分配模式
    pub async fn set_plan_assignment_mode(&self, mode: PlanAssignmentMode, user_id: i32) -> Result<SystemConfig, String> {
        let request = UpdateSystemConfigRequest {
            config_value: Some(mode.to_string()),
            config_type: None,
            description: None,
            category: None,
            is_active: None,
        };

        self.update_config(CONFIG_PLAN_ASSIGNMENT_MODE, request, user_id).await
    }

    // 删除配置
    pub async fn delete_config(&self, config_key: &str) -> Result<bool, String> {
        let result = sqlx::query!(
            "DELETE FROM system_configs WHERE config_key = $1",
            config_key
        )
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    // 获取按类别分组的配置
    pub async fn get_configs_by_category(&self) -> Result<std::collections::HashMap<String, Vec<SystemConfig>>, String> {
        let configs = self.get_all_configs(Some(SystemConfigQuery {
            category: None,
            config_key: None,
            is_active: Some(true),
        })).await?;

        let mut grouped = std::collections::HashMap::new();
        for config in configs {
            grouped.entry(config.category.clone())
                .or_insert_with(Vec::new)
                .push(config);
        }

        Ok(grouped)
    }
}
