use crate::models::plan_task::{
    CreatePlanTaskRequest, CreatePlanTasksFromWorkOrderRequest, GanttChartData, GanttMachine,
    GanttSkillGroup, GanttTask, GanttTimeRange, PlanTask, PlanTaskQuery, PlanTaskSearchResult,
    PlanTaskWithDetails, UpdatePlanTaskRequest, get_valid_plan_task_statuses,
};
use crate::services::system_config_service::SystemConfigService;
use chrono::{DateTime, Duration, Utc};
use sqlx::PgPool;

pub struct PlanTaskService {
    pool: PgPool,
    system_config_service: SystemConfigService,
}

impl PlanTaskService {
    pub fn new(pool: PgPool) -> Self {
        let system_config_service = SystemConfigService::new(pool.clone());
        Self { pool, system_config_service }
    }

    pub async fn create_plan_task(
        &self,
        request: CreatePlanTaskRequest,
    ) -> Result<PlanTask, String> {
        // Validate work order exists
        let work_order_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM work_orders WHERE id = $1)",
            request.work_order_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if !work_order_exists.unwrap_or(false) {
            return Err("Work order not found".to_string());
        }

        // Validate routing step exists
        let routing_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM routings WHERE id = $1)",
            request.routing_step_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if !routing_exists.unwrap_or(false) {
            return Err("Routing step not found".to_string());
        }

        // Validate skill group exists
        let skill_group_exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM skill_groups WHERE id = $1)",
            request.skill_group_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if !skill_group_exists.unwrap_or(false) {
            return Err("Skill group not found".to_string());
        }

        // Validate machine if specified
        if let Some(machine_id) = request.machine_id {
            let machine_exists = sqlx::query_scalar!(
                "SELECT EXISTS(SELECT 1 FROM machines WHERE id = $1 AND skill_group_id = $2)",
                machine_id,
                request.skill_group_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            if !machine_exists.unwrap_or(false) {
                return Err("Machine not found or does not belong to the specified skill group".to_string());
            }
        }

        // Validate time range
        if request.planned_end <= request.planned_start {
            return Err("Planned end time must be after planned start time".to_string());
        }

        // Validate minimum 30-minute duration
        let duration = request.planned_end - request.planned_start;
        if duration.num_minutes() < 30 {
            return Err("Minimum task duration is 30 minutes".to_string());
        }

        let plan_task = sqlx::query_as!(
            PlanTask,
            "INSERT INTO plan_tasks (work_order_id, routing_step_id, skill_group_id, machine_id, planned_start, planned_end)
             VALUES ($1, $2, $3, $4, $5, $6)
             RETURNING id, work_order_id, routing_step_id, skill_group_id, machine_id, planned_start, planned_end, status",
            request.work_order_id,
            request.routing_step_id,
            request.skill_group_id,
            request.machine_id,
            request.planned_start,
            request.planned_end
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(plan_task)
    }

    pub async fn get_plan_task_by_id(
        &self,
        plan_task_id: i32,
    ) -> Result<Option<PlanTaskWithDetails>, String> {
        let row = sqlx::query!(
            "SELECT pt.id, pt.work_order_id, pt.routing_step_id, pt.skill_group_id, pt.machine_id,
                    pt.planned_start, pt.planned_end, pt.status,
                    wo.quantity as work_order_quantity, wo.status as work_order_status, wo.due_date as work_order_due_date,
                    pb.project_id, p.project_name, p.customer_name,
                    pb.part_id, parts.part_number, parts.part_name, parts.version,
                    r.step_number, r.process_name, r.work_instructions, r.standard_hours,
                    sg.group_name as skill_group_name,
                    m.machine_name as \"machine_name: Option<String>\"
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN projects p ON pb.project_id = p.id
             JOIN parts ON pb.part_id = parts.id
             JOIN routings r ON pt.routing_step_id = r.id
             JOIN skill_groups sg ON pt.skill_group_id = sg.id
             LEFT JOIN machines m ON pt.machine_id = m.id
             WHERE pt.id = $1",
            plan_task_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if let Some(row) = row {
            let plan_task = PlanTaskWithDetails {
                id: row.id,
                work_order_id: row.work_order_id,
                routing_step_id: row.routing_step_id,
                skill_group_id: row.skill_group_id,
                machine_id: row.machine_id,
                planned_start: row.planned_start,
                planned_end: row.planned_end,
                status: row.status,
                work_order_quantity: row.work_order_quantity,
                work_order_status: row.work_order_status,
                work_order_due_date: row.work_order_due_date,
                project_id: row.project_id,
                project_name: row.project_name,
                customer_name: row.customer_name,
                part_id: row.part_id,
                part_number: row.part_number,
                part_name: row.part_name,
                version: row.version,
                step_number: row.step_number,
                process_name: row.process_name,
                work_instructions: row.work_instructions,
                standard_hours: row
                    .standard_hours
                    .map(|bd| bd.to_string().parse().unwrap_or(0.0)),
                skill_group_name: row.skill_group_name,
                machine_name: row.machine_name,
            };
            Ok(Some(plan_task))
        } else {
            Ok(None)
        }
    }

    pub async fn get_all_plan_tasks(
        &self,
        query: PlanTaskQuery,
    ) -> Result<PlanTaskSearchResult, String> {
        let limit = query.limit.unwrap_or(50).min(100);
        let offset = query.offset.unwrap_or(0);

        // Build WHERE clause dynamically
        let mut where_conditions = Vec::new();
        let mut param_count = 0;

        if query.work_order_id.is_some() {
            param_count += 1;
            where_conditions.push(format!("pt.work_order_id = ${}", param_count));
        }

        if query.skill_group_id.is_some() {
            param_count += 1;
            where_conditions.push(format!("pt.skill_group_id = ${}", param_count));
        }

        if query.machine_id.is_some() {
            param_count += 1;
            where_conditions.push(format!("pt.machine_id = ${}", param_count));
        }

        if query.status.is_some() {
            param_count += 1;
            where_conditions.push(format!("pt.status = ${}", param_count));
        }

        if query.start_date.is_some() {
            param_count += 1;
            where_conditions.push(format!("pt.planned_start >= ${}", param_count));
        }

        if query.end_date.is_some() {
            param_count += 1;
            where_conditions.push(format!("pt.planned_end <= ${}", param_count));
        }

        let _where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };
        // TODO: Use where_clause in the actual query

        // Get total count
        let total_count = if where_conditions.is_empty() {
            sqlx::query_scalar!("SELECT COUNT(*) FROM plan_tasks")
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?
                .unwrap_or(0)
        } else {
            // For simplicity, we'll use a basic count for filtered queries
            // In a real implementation, you'd want to build dynamic queries properly
            self.execute_filtered_count(&query).await?
        };

        // Get plan tasks - simplified to avoid BigDecimal conversion issues
        let plan_tasks = if where_conditions.is_empty() {
            let rows = sqlx::query!(
                "SELECT pt.id, pt.work_order_id, pt.routing_step_id, pt.skill_group_id, pt.machine_id,
                        pt.planned_start, pt.planned_end, pt.status,
                        wo.quantity as work_order_quantity, wo.status as work_order_status, wo.due_date as work_order_due_date,
                        pb.project_id, p.project_name, p.customer_name,
                        pb.part_id, parts.part_number, parts.part_name, parts.version,
                        r.step_number, r.process_name, r.work_instructions, r.standard_hours,
                        sg.group_name as skill_group_name,
                        m.machine_name as \"machine_name: Option<String>\"
                 FROM plan_tasks pt
                 JOIN work_orders wo ON pt.work_order_id = wo.id
                 JOIN project_boms pb ON wo.project_bom_id = pb.id
                 JOIN projects p ON pb.project_id = p.id
                 JOIN parts ON pb.part_id = parts.id
                 JOIN routings r ON pt.routing_step_id = r.id
                 JOIN skill_groups sg ON pt.skill_group_id = sg.id
                 LEFT JOIN machines m ON pt.machine_id = m.id
                 ORDER BY pt.planned_start ASC
                 LIMIT $1 OFFSET $2",
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            rows.into_iter()
                .map(|row| PlanTaskWithDetails {
                    id: row.id,
                    work_order_id: row.work_order_id,
                    routing_step_id: row.routing_step_id,
                    skill_group_id: row.skill_group_id,
                    machine_id: row.machine_id,
                    planned_start: row.planned_start,
                    planned_end: row.planned_end,
                    status: row.status,
                    work_order_quantity: row.work_order_quantity,
                    work_order_status: row.work_order_status,
                    work_order_due_date: row.work_order_due_date,
                    project_id: row.project_id,
                    project_name: row.project_name,
                    customer_name: row.customer_name,
                    part_id: row.part_id,
                    part_number: row.part_number,
                    part_name: row.part_name,
                    version: row.version,
                    step_number: row.step_number,
                    process_name: row.process_name,
                    work_instructions: row.work_instructions,
                    standard_hours: row
                        .standard_hours
                        .and_then(|bd| bd.to_string().parse().ok()),
                    skill_group_name: row.skill_group_name,
                    machine_name: row.machine_name,
                })
                .collect()
        } else {
            self.execute_filtered_query(&query, limit, offset).await?
        };

        Ok(PlanTaskSearchResult {
            plan_tasks,
            total_count,
            limit,
            offset,
        })
    }

    async fn execute_filtered_count(&self, query: &PlanTaskQuery) -> Result<i64, String> {
        // Simplified implementation for common filter combinations
        if let Some(work_order_id) = query.work_order_id {
            if let Some(status) = &query.status {
                sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM plan_tasks WHERE work_order_id = $1 AND status = $2",
                    work_order_id,
                    status
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))
                .map(|count| count.unwrap_or(0))
            } else {
                sqlx::query_scalar!(
                    "SELECT COUNT(*) FROM plan_tasks WHERE work_order_id = $1",
                    work_order_id
                )
                .fetch_one(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))
                .map(|count| count.unwrap_or(0))
            }
        } else if let Some(skill_group_id) = query.skill_group_id {
            sqlx::query_scalar!(
                "SELECT COUNT(*) FROM plan_tasks WHERE skill_group_id = $1",
                skill_group_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))
            .map(|count| count.unwrap_or(0))
        } else {
            Ok(0)
        }
    }

    async fn execute_filtered_query(
        &self,
        query: &PlanTaskQuery,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<PlanTaskWithDetails>, String> {
        // Simplified implementation for common filter combinations
        if let Some(work_order_id) = query.work_order_id {
            if let Some(status) = &query.status {
                let rows = sqlx::query!(
                    "SELECT pt.id, pt.work_order_id, pt.routing_step_id, pt.skill_group_id, pt.machine_id,
                            pt.planned_start, pt.planned_end, pt.status,
                            wo.quantity as work_order_quantity, wo.status as work_order_status, wo.due_date as work_order_due_date,
                            pb.project_id, p.project_name, p.customer_name,
                            pb.part_id, parts.part_number, parts.part_name, parts.version,
                            r.step_number, r.process_name, r.work_instructions, r.standard_hours,
                            sg.group_name as skill_group_name,
                            m.machine_name as \"machine_name: Option<String>\"
                     FROM plan_tasks pt
                     JOIN work_orders wo ON pt.work_order_id = wo.id
                     JOIN project_boms pb ON wo.project_bom_id = pb.id
                     JOIN projects p ON pb.project_id = p.id
                     JOIN parts ON pb.part_id = parts.id
                     JOIN routings r ON pt.routing_step_id = r.id
                     JOIN skill_groups sg ON pt.skill_group_id = sg.id
                     LEFT JOIN machines m ON pt.machine_id = m.id
                     WHERE pt.work_order_id = $1 AND pt.status = $2
                     ORDER BY pt.planned_start ASC
                     LIMIT $3 OFFSET $4",
                    work_order_id, status, limit, offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                Ok(rows
                    .into_iter()
                    .map(|row| PlanTaskWithDetails {
                        id: row.id,
                        work_order_id: row.work_order_id,
                        routing_step_id: row.routing_step_id,
                        skill_group_id: row.skill_group_id,
                        planned_start: row.planned_start,
                        planned_end: row.planned_end,
                        status: row.status,
                        work_order_quantity: row.work_order_quantity,
                        work_order_status: row.work_order_status,
                        work_order_due_date: row.work_order_due_date,
                        project_id: row.project_id,
                        project_name: row.project_name,
                        customer_name: row.customer_name,
                        part_id: row.part_id,
                        part_number: row.part_number,
                        part_name: row.part_name,
                        version: row.version,
                        step_number: row.step_number,
                        process_name: row.process_name,
                        work_instructions: row.work_instructions,
                        standard_hours: row
                            .standard_hours
                            .map(|bd| bd.to_string().parse().unwrap_or(0.0)),
                        skill_group_name: row.skill_group_name,
                        machine_id: row.machine_id,
                        machine_name: row.machine_name,
                    })
                    .collect())
            } else {
                let rows = sqlx::query!(
                    "SELECT pt.id, pt.work_order_id, pt.routing_step_id, pt.skill_group_id, pt.machine_id,
                            pt.planned_start, pt.planned_end, pt.status,
                            wo.quantity as work_order_quantity, wo.status as work_order_status, wo.due_date as work_order_due_date,
                            pb.project_id, p.project_name, p.customer_name,
                            pb.part_id, parts.part_number, parts.part_name, parts.version,
                            r.step_number, r.process_name, r.work_instructions, r.standard_hours,
                            sg.group_name as skill_group_name,
                            m.machine_name as \"machine_name: Option<String>\"
                     FROM plan_tasks pt
                     JOIN work_orders wo ON pt.work_order_id = wo.id
                     JOIN project_boms pb ON wo.project_bom_id = pb.id
                     JOIN projects p ON pb.project_id = p.id
                     JOIN parts ON pb.part_id = parts.id
                     JOIN routings r ON pt.routing_step_id = r.id
                     JOIN skill_groups sg ON pt.skill_group_id = sg.id
                     LEFT JOIN machines m ON pt.machine_id = m.id
                     WHERE pt.work_order_id = $1
                     ORDER BY pt.planned_start ASC
                     LIMIT $2 OFFSET $3",
                    work_order_id, limit, offset
                )
                .fetch_all(&self.pool)
                .await
                .map_err(|e| format!("Database error: {}", e))?;

                Ok(rows
                    .into_iter()
                    .map(|row| PlanTaskWithDetails {
                        id: row.id,
                        work_order_id: row.work_order_id,
                        routing_step_id: row.routing_step_id,
                        skill_group_id: row.skill_group_id,
                        planned_start: row.planned_start,
                        planned_end: row.planned_end,
                        status: row.status,
                        work_order_quantity: row.work_order_quantity,
                        work_order_status: row.work_order_status,
                        work_order_due_date: row.work_order_due_date,
                        project_id: row.project_id,
                        project_name: row.project_name,
                        customer_name: row.customer_name,
                        part_id: row.part_id,
                        part_number: row.part_number,
                        part_name: row.part_name,
                        version: row.version,
                        step_number: row.step_number,
                        process_name: row.process_name,
                        work_instructions: row.work_instructions,
                        standard_hours: row
                            .standard_hours
                            .map(|bd| bd.to_string().parse().unwrap_or(0.0)),
                        skill_group_name: row.skill_group_name,
                        machine_id: row.machine_id,
                        machine_name: row.machine_name,
                    })
                    .collect())
            }
        } else {
            Ok(Vec::new())
        }
    }

    pub async fn update_plan_task(
        &self,
        plan_task_id: i32,
        request: UpdatePlanTaskRequest,
    ) -> Result<Option<PlanTask>, String> {
        // Validate status if provided
        if let Some(ref status) = request.status {
            let valid_statuses = get_valid_plan_task_statuses();
            if !valid_statuses.contains(&status.as_str()) {
                return Err(format!(
                    "Invalid status. Valid statuses are: {:?}",
                    valid_statuses
                ));
            }
        }

        // Validate skill group if provided
        if let Some(skill_group_id) = request.skill_group_id {
            let skill_group_exists = sqlx::query_scalar!(
                "SELECT EXISTS(SELECT 1 FROM skill_groups WHERE id = $1)",
                skill_group_id
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            if !skill_group_exists.unwrap_or(false) {
                return Err("Skill group not found".to_string());
            }
        }

        // Validate time range if both start and end are provided
        if let (Some(start), Some(end)) = (&request.planned_start, &request.planned_end) {
            if end <= start {
                return Err("Planned end time must be after planned start time".to_string());
            }

            // Validate minimum 30-minute duration
            let duration = *end - *start;
            if duration.num_minutes() < 30 {
                return Err("Minimum task duration is 30 minutes".to_string());
            }
        }

        // Check if plan task exists
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM plan_tasks WHERE id = $1)",
            plan_task_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if !exists.unwrap_or(false) {
            return Ok(None);
        }

        // Get current values for fields not being updated
        let current = sqlx::query_as!(
            PlanTask,
            "SELECT id, work_order_id, routing_step_id, skill_group_id, machine_id, planned_start, planned_end, status
             FROM plan_tasks WHERE id = $1",
            plan_task_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let plan_task = sqlx::query_as!(
            PlanTask,
            "UPDATE plan_tasks
             SET skill_group_id = $1, machine_id = $2, planned_start = $3, planned_end = $4, status = $5
             WHERE id = $6
             RETURNING id, work_order_id, routing_step_id, skill_group_id, machine_id, planned_start, planned_end, status",
            request.skill_group_id.unwrap_or(current.skill_group_id),
            request.machine_id.unwrap_or(current.machine_id),
            request.planned_start.unwrap_or(current.planned_start),
            request.planned_end.unwrap_or(current.planned_end),
            request.status.unwrap_or(current.status),
            plan_task_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        Ok(Some(plan_task))
    }

    pub async fn delete_plan_task(&self, plan_task_id: i32) -> Result<bool, String> {
        let result = sqlx::query!("DELETE FROM plan_tasks WHERE id = $1", plan_task_id)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn create_plan_tasks_from_work_order(
        &self,
        request: CreatePlanTasksFromWorkOrderRequest,
    ) -> Result<Vec<PlanTask>, String> {
        // Get work order details and validate it exists
        let work_order = sqlx::query!(
            "SELECT wo.id, wo.quantity, pb.part_id
             FROM work_orders wo
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             WHERE wo.id = $1",
            request.work_order_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let work_order = work_order.ok_or("Work order not found")?;

        // Get routing steps for the part
        let routing_steps = sqlx::query!(
            "SELECT id, step_number, process_name, standard_hours
             FROM routings
             WHERE part_id = $1
             ORDER BY step_number",
            work_order.part_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        if routing_steps.is_empty() {
            return Err("No routing steps found for this part".to_string());
        }

        let mut plan_tasks = Vec::new();
        let mut current_start = request.start_date;

        for step in routing_steps {
            // Determine assignment mode and get skill group and machine
            let (skill_group_id, machine_id) = if let Some(ref machine_assignments) = request.machine_assignments {
                // Machine assignment mode
                if let Some(assignment) = machine_assignments.iter().find(|a| a.routing_step_id == step.id) {
                    // Get skill group for the machine
                    let machine_info = sqlx::query!(
                        "SELECT skill_group_id FROM machines WHERE id = $1",
                        assignment.machine_id
                    )
                    .fetch_optional(&self.pool)
                    .await
                    .map_err(|e| format!("Database error: {}", e))?;

                    if let Some(info) = machine_info {
                        (info.skill_group_id, Some(assignment.machine_id))
                    } else {
                        return Err(format!("Machine {} not found", assignment.machine_id));
                    }
                } else {
                    (1, None) // Default skill group, no machine
                }
            } else if let Some(ref skill_assignments) = request.skill_group_assignments {
                // Skill group assignment mode
                let skill_group_id = skill_assignments
                    .iter()
                    .find(|a| a.routing_step_id == step.id)
                    .map(|a| a.skill_group_id)
                    .unwrap_or(1); // Default to first skill group if not specified
                (skill_group_id, None)
            } else {
                (1, None) // Default skill group, no machine
            };

            // Calculate duration based on standard hours (default to 1 hour if not specified)
            let duration_hours = step
                .standard_hours
                .map(|bd| bd.to_string().parse::<f64>().unwrap_or(1.0))
                .unwrap_or(1.0);
            let duration = Duration::hours(duration_hours as i64);
            let planned_end = current_start + duration;

            let plan_task = sqlx::query_as!(
                PlanTask,
                "INSERT INTO plan_tasks (work_order_id, routing_step_id, skill_group_id, machine_id, planned_start, planned_end)
                 VALUES ($1, $2, $3, $4, $5, $6)
                 RETURNING id, work_order_id, routing_step_id, skill_group_id, machine_id, planned_start, planned_end, status",
                request.work_order_id,
                step.id,
                skill_group_id,
                machine_id,
                current_start,
                planned_end
            )
            .fetch_one(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            plan_tasks.push(plan_task);
            current_start = planned_end; // Next task starts when this one ends
        }

        Ok(plan_tasks)
    }

    pub async fn get_gantt_chart_data(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<GanttChartData, String> {
        // Get tasks within the time range
        let tasks = sqlx::query!(
            "SELECT pt.id, pt.planned_start, pt.planned_end, pt.skill_group_id, pt.machine_id, pt.work_order_id, pt.status,
                    parts.part_number, r.process_name, m.machine_name as \"machine_name: Option<String>\"
             FROM plan_tasks pt
             JOIN work_orders wo ON pt.work_order_id = wo.id
             JOIN project_boms pb ON wo.project_bom_id = pb.id
             JOIN parts ON pb.part_id = parts.id
             JOIN routings r ON pt.routing_step_id = r.id
             LEFT JOIN machines m ON pt.machine_id = m.id
             WHERE pt.planned_start <= $2 AND pt.planned_end >= $1
             ORDER BY pt.planned_start",
            start_date,
            end_date
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let gantt_tasks: Vec<GanttTask> = tasks
            .into_iter()
            .map(|task| GanttTask {
                id: task.id,
                name: format!("{} - {}", task.part_number, task.process_name),
                start: task.planned_start,
                end: task.planned_end,
                skill_group_id: task.skill_group_id,
                machine_id: task.machine_id,
                work_order_id: task.work_order_id,
                part_number: task.part_number,
                process_name: task.process_name,
                status: task.status,
                progress: 0.0, // TODO: Calculate actual progress
                machine_name: task.machine_name,
            })
            .collect();

        // Get skill groups with their machines
        let skill_groups = sqlx::query!(
            "SELECT sg.id, sg.group_name
             FROM skill_groups sg
             ORDER BY sg.group_name"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Database error: {}", e))?;

        let mut gantt_skill_groups = Vec::new();
        for sg in skill_groups {
            let machines = sqlx::query!(
                "SELECT id, machine_name, status FROM machines WHERE skill_group_id = $1",
                sg.id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| format!("Database error: {}", e))?;

            let gantt_machines: Vec<GanttMachine> = machines
                .into_iter()
                .map(|m| GanttMachine {
                    id: m.id,
                    name: m.machine_name,
                    status: m.status,
                })
                .collect();

            gantt_skill_groups.push(GanttSkillGroup {
                id: sg.id,
                name: sg.group_name,
                machines: gantt_machines,
            });
        }

        Ok(GanttChartData {
            tasks: gantt_tasks,
            skill_groups: gantt_skill_groups,
            time_range: GanttTimeRange {
                start: start_date,
                end: end_date,
            },
        })
    }
}
