use crate::models::user::User;
use crate::utils::jwt::verify_jwt;
use sqlx::PgPool;

pub async fn get_user_from_token(pool: &PgPool, token: &str) -> Result<User, String> {
    // 解码JWT token获取用户ID
    let claims = verify_jwt(token).map_err(|_| "Invalid token".to_string())?;
    let user_id = claims.sub.parse::<i32>().map_err(|_| "Invalid user ID in token".to_string())?;

    // 从数据库获取用户信息
    let user = sqlx::query_as!(
        User,
        "SELECT id, username, password_hash, full_name, is_active, created_at FROM users WHERE id = $1 AND is_active = true",
        user_id
    )
    .fetch_optional(pool)
    .await
    .map_err(|e| format!("Database error: {}", e))?
    .ok_or("User not found".to_string())?;

    Ok(user)
}
