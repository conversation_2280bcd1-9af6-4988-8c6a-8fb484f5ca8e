use axum::{
    extract::{Extension, Query, State},
    http::StatusCode,
    response::J<PERSON>,
};
use sqlx::PgPool;

use crate::{
    middleware::auth::AuthUser,
    models::execution_log::{
        BarcodeValidationRequest, CreateExecutionLogRequest, ExecutionLogQuery,
        TaskExecutionRequest,
    },
    services::execution_service::ExecutionService,
    utils::validation::ErrorResponse,
};

pub async fn create_execution_log(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateExecutionLogRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let execution_service = ExecutionService::new(pool);

    match execution_service
        .create_execution_log(auth_user.id, request)
        .await
    {
        Ok(execution_log) => Ok(Json(serde_json::json!({
            "message": "Execution log created successfully",
            "execution_log": execution_log
        }))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "execution_log_creation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn start_task(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<TaskExecutionRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only operators and above can start tasks
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
        && !auth_user.roles.contains(&"operator".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only operators and above can start tasks".to_string(),
            }),
        ));
    }

    let execution_service = ExecutionService::new(pool);

    match execution_service.start_task(auth_user.id, request).await {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "task_start_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn complete_task(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<TaskExecutionRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only operators and above can complete tasks
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
        && !auth_user.roles.contains(&"operator".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only operators and above can complete tasks".to_string(),
            }),
        ));
    }

    let execution_service = ExecutionService::new(pool);

    match execution_service.complete_task(auth_user.id, request).await {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "task_complete_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn pause_task(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<TaskExecutionRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only operators and above can pause tasks
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
        && !auth_user.roles.contains(&"operator".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only operators and above can pause tasks".to_string(),
            }),
        ));
    }

    let execution_service = ExecutionService::new(pool);

    match execution_service.pause_task(auth_user.id, request).await {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "task_pause_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn resume_task(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<TaskExecutionRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    // Only operators and above can resume tasks
    if !auth_user.roles.contains(&"admin".to_string())
        && !auth_user.roles.contains(&"process_engineer".to_string())
        && !auth_user.roles.contains(&"planner".to_string())
        && !auth_user.roles.contains(&"operator".to_string())
    {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                error: "insufficient_permissions".to_string(),
                message: "Only operators and above can resume tasks".to_string(),
            }),
        ));
    }

    let execution_service = ExecutionService::new(pool);

    match execution_service.resume_task(auth_user.id, request).await {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "task_resume_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn validate_barcode(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Json(request): Json<BarcodeValidationRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let execution_service = ExecutionService::new(pool);

    match execution_service.validate_barcode(request).await {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "barcode_validation_failed".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_active_tasks(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let execution_service = ExecutionService::new(pool);

    match execution_service.get_active_tasks(auth_user.id).await {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_shop_floor_dashboard(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(params): Query<serde_json::Value>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let skill_group_id = params
        .get("skill_group_id")
        .and_then(|v| v.as_i64())
        .map(|v| v as i32);

    let execution_service = ExecutionService::new(pool);

    match execution_service
        .get_shop_floor_dashboard(skill_group_id)
        .await
    {
        Ok(response) => Ok(Json(serde_json::json!(response))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_execution_logs(
    State(pool): State<PgPool>,
    Extension(_auth_user): Extension<AuthUser>,
    Query(query): Query<ExecutionLogQuery>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let execution_service = ExecutionService::new(pool);

    match execution_service.get_execution_logs(query).await {
        Ok(result) => Ok(Json(serde_json::json!({"data": result}))),
        Err(error) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: error,
            }),
        )),
    }
}

pub async fn get_my_skill_group_tasks(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    use crate::services::system_config_service::SystemConfigService;

    // Check the current plan assignment mode
    let system_config_service = SystemConfigService::new(pool.clone());
    let assignment_mode = system_config_service.get_plan_assignment_mode().await
        .map_err(|e| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "config_error".to_string(),
                    message: format!("Failed to get assignment mode: {}", e),
                }),
            )
        })?;

    match assignment_mode {
        crate::models::system_config::PlanAssignmentMode::Machine => {
            // Machine assignment mode - get tasks assigned to user's machines
            get_my_machine_tasks_impl(pool, auth_user.id).await
        }
        crate::models::system_config::PlanAssignmentMode::SkillGroup => {
            // Skill group assignment mode - get tasks assigned to user's skill groups
            get_my_skill_group_tasks_impl(pool, auth_user.id).await
        }
    }
}

async fn get_my_skill_group_tasks_impl(
    pool: PgPool,
    user_id: i32,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    use chrono::{Duration, Utc};

    // Calculate the time range: from now to 3 days in the future
    let now = Utc::now();
    let three_days_later = now + Duration::days(3);

    // Get user's skill groups
    let user_skill_groups = sqlx::query!(
        "SELECT sg.id FROM skill_groups sg
         JOIN user_skills us ON sg.id = us.skill_group_id
         WHERE us.user_id = $1",
        user_id
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to get user skill groups: {}", e),
            }),
        )
    })?;

    if user_skill_groups.is_empty() {
        return Ok(Json(serde_json::json!({ "tasks": [] })));
    }

    let skill_group_ids: Vec<i32> = user_skill_groups.iter().map(|sg| sg.id).collect();

    // Get plan tasks for user's skill groups within the next 3 days
    let tasks = sqlx::query!(
        "SELECT pt.id, pt.work_order_id, pt.routing_step_id, pt.skill_group_id, pt.machine_id,
                pt.planned_start, pt.planned_end, pt.status,
                wo.quantity as work_order_quantity, wo.status as work_order_status, wo.due_date as work_order_due_date,
                pb.project_id, p.project_name, p.customer_name,
                pb.part_id, parts.part_number, parts.part_name, parts.version,
                r.step_number, r.process_name, r.work_instructions, r.standard_hours,
                sg.group_name as skill_group_name,
                m.machine_name as \"machine_name: Option<String>\"
         FROM plan_tasks pt
         JOIN work_orders wo ON pt.work_order_id = wo.id
         JOIN project_boms pb ON wo.project_bom_id = pb.id
         JOIN projects p ON pb.project_id = p.id
         JOIN parts ON pb.part_id = parts.id
         JOIN routings r ON pt.routing_step_id = r.id
         JOIN skill_groups sg ON pt.skill_group_id = sg.id
         LEFT JOIN machines m ON pt.machine_id = m.id
         WHERE pt.skill_group_id = ANY($1)
         AND pt.status IN ('planned', 'scheduled', 'in_progress')
         AND pt.planned_start >= $2
         AND pt.planned_start <= $3
         ORDER BY pt.planned_start ASC",
        &skill_group_ids,
        now,
        three_days_later
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to get tasks: {}", e),
            }),
        )
    })?;

    let task_details: Vec<serde_json::Value> = tasks
        .into_iter()
        .map(|task| {
            serde_json::json!({
                "id": task.id,
                "work_order_id": task.work_order_id,
                "routing_step_id": task.routing_step_id,
                "skill_group_id": task.skill_group_id,
                "machine_id": task.machine_id,
                "planned_start": task.planned_start,
                "planned_end": task.planned_end,
                "status": task.status,
                "work_order_quantity": task.work_order_quantity,
                "work_order_status": task.work_order_status,
                "work_order_due_date": task.work_order_due_date,
                "project_id": task.project_id,
                "project_name": task.project_name,
                "customer_name": task.customer_name,
                "part_id": task.part_id,
                "part_number": task.part_number,
                "part_name": task.part_name,
                "version": task.version,
                "step_number": task.step_number,
                "process_name": task.process_name,
                "work_instructions": task.work_instructions,
                "standard_hours": task.standard_hours.map(|h| h.to_string()),
                "skill_group_name": task.skill_group_name,
                "machine_name": task.machine_name
            })
        })
        .collect();

    Ok(Json(serde_json::json!({ "tasks": task_details })))
}

async fn get_my_machine_tasks_impl(
    pool: PgPool,
    user_id: i32,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    use chrono::{Duration, Utc};

    // Calculate the time range: from now to 3 days in the future
    let now = Utc::now();
    let three_days_later = now + Duration::days(3);

    // Get user's skill groups first
    let user_skill_groups = sqlx::query!(
        "SELECT sg.id FROM skill_groups sg
         JOIN user_skills us ON sg.id = us.skill_group_id
         WHERE us.user_id = $1",
        user_id
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to get user skill groups: {}", e),
            }),
        )
    })?;

    if user_skill_groups.is_empty() {
        return Ok(Json(serde_json::json!({ "tasks": [] })));
    }

    let skill_group_ids: Vec<i32> = user_skill_groups.iter().map(|sg| sg.id).collect();

    // Get machines that the user can operate (based on skill groups)
    let user_machines = sqlx::query!(
        "SELECT id FROM machines WHERE skill_group_id = ANY($1)",
        &skill_group_ids
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to get user machines: {}", e),
            }),
        )
    })?;

    if user_machines.is_empty() {
        return Ok(Json(serde_json::json!({ "tasks": [] })));
    }

    let machine_ids: Vec<i32> = user_machines.iter().map(|m| m.id).collect();

    // Get plan tasks assigned to user's machines within the next 3 days
    let tasks = sqlx::query!(
        "SELECT pt.id, pt.work_order_id, pt.routing_step_id, pt.skill_group_id, pt.machine_id,
                pt.planned_start, pt.planned_end, pt.status,
                wo.quantity as work_order_quantity, wo.status as work_order_status, wo.due_date as work_order_due_date,
                pb.project_id, p.project_name, p.customer_name,
                pb.part_id, parts.part_number, parts.part_name, parts.version,
                r.step_number, r.process_name, r.work_instructions, r.standard_hours,
                sg.group_name as skill_group_name,
                m.machine_name as \"machine_name: Option<String>\"
         FROM plan_tasks pt
         JOIN work_orders wo ON pt.work_order_id = wo.id
         JOIN project_boms pb ON wo.project_bom_id = pb.id
         JOIN projects p ON pb.project_id = p.id
         JOIN parts ON pb.part_id = parts.id
         JOIN routings r ON pt.routing_step_id = r.id
         JOIN skill_groups sg ON pt.skill_group_id = sg.id
         LEFT JOIN machines m ON pt.machine_id = m.id
         WHERE pt.machine_id = ANY($1)
         AND pt.status IN ('planned', 'scheduled', 'in_progress')
         AND pt.planned_start >= $2
         AND pt.planned_start <= $3
         ORDER BY pt.planned_start ASC",
        &machine_ids,
        now,
        three_days_later
    )
    .fetch_all(&pool)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "database_error".to_string(),
                message: format!("Failed to get machine tasks: {}", e),
            }),
        )
    })?;

    let task_details: Vec<serde_json::Value> = tasks
        .into_iter()
        .map(|task| {
            serde_json::json!({
                "id": task.id,
                "work_order_id": task.work_order_id,
                "routing_step_id": task.routing_step_id,
                "skill_group_id": task.skill_group_id,
                "machine_id": task.machine_id,
                "planned_start": task.planned_start,
                "planned_end": task.planned_end,
                "status": task.status,
                "work_order_quantity": task.work_order_quantity,
                "work_order_status": task.work_order_status,
                "work_order_due_date": task.work_order_due_date,
                "project_id": task.project_id,
                "project_name": task.project_name,
                "customer_name": task.customer_name,
                "part_id": task.part_id,
                "part_number": task.part_number,
                "part_name": task.part_name,
                "version": task.version,
                "step_number": task.step_number,
                "process_name": task.process_name,
                "work_instructions": task.work_instructions,
                "standard_hours": task.standard_hours.map(|h| h.to_string()),
                "skill_group_name": task.skill_group_name,
                "machine_name": task.machine_name
            })
        })
        .collect();

    Ok(Json(serde_json::json!({ "tasks": task_details })))
}

