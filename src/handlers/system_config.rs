use crate::models::system_config::{
    CreateSystemConfigRequest, UpdateSystemConfigRequest, SystemConfigQuery,
    PlanAssignmentMode,
};
use crate::services::system_config_service::SystemConfigService;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use serde_json::{json, Value};
use sqlx::PgPool;

// 获取所有系统配置
pub async fn get_system_configs(
    State(pool): State<PgPool>,
    Query(query): Query<SystemConfigQuery>,
    Extension(auth_user): Extension<crate::middleware::auth::AuthUser>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 用户已通过认证中间件验证

    // 检查管理员权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let service = SystemConfigService::new(pool);
    let configs = service.get_all_configs(Some(query)).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!({
        "configs": configs,
        "total": configs.len()
    })))
}

// 获取按类别分组的配置
pub async fn get_configs_by_category(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<crate::middleware::auth::AuthUser>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 用户已通过认证中间件验证

    // 检查管理员权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let service = SystemConfigService::new(pool);
    let grouped_configs = service.get_configs_by_category().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!(grouped_configs)))
}

// 获取特定配置
pub async fn get_config_by_key(
    State(pool): State<PgPool>,
    Path(config_key): Path<String>,
    Extension(auth_user): Extension<crate::middleware::auth::AuthUser>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 用户已通过认证中间件验证

    let service = SystemConfigService::new(pool);
    let config = service.get_typed_config(&config_key).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    match config {
        Some(config) => Ok(Json(json!(config))),
        None => Err((
            StatusCode::NOT_FOUND,
            Json(json!({"error": "Configuration not found"})),
        )),
    }
}

// 获取计划分配模式配置
pub async fn get_plan_assignment_mode(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<crate::middleware::auth::AuthUser>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 用户已通过认证中间件验证

    let service = SystemConfigService::new(pool);
    let mode = service.get_plan_assignment_mode().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    let enabled = service.is_plan_assignment_mode_enabled().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!({
        "mode": mode,
        "enabled": enabled
    })))
}

// 创建新配置
pub async fn create_config(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<crate::middleware::auth::AuthUser>,
    Json(request): Json<CreateSystemConfigRequest>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 检查管理员权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let service = SystemConfigService::new(pool);
    let config = service.create_config(request, auth_user.id).await.map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!(config)))
}

// 更新配置
pub async fn update_config(
    State(pool): State<PgPool>,
    Path(config_key): Path<String>,
    Extension(auth_user): Extension<crate::middleware::auth::AuthUser>,
    Json(request): Json<UpdateSystemConfigRequest>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 检查管理员权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let service = SystemConfigService::new(pool);
    let config = service.update_config(&config_key, request, auth_user.id).await.map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!(config)))
}

// 设置计划分配模式
pub async fn set_plan_assignment_mode(
    State(pool): State<PgPool>,
    Extension(auth_user): Extension<crate::middleware::auth::AuthUser>,
    Json(payload): Json<Value>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 检查管理员权限
    if !auth_user.roles.contains(&"admin".to_string()) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let mode_str = payload["mode"].as_str().ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({"error": "Missing or invalid mode field"})),
        )
    })?;

    let mode = PlanAssignmentMode::from_string(mode_str).map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({"error": e})),
        )
    })?;

    let service = SystemConfigService::new(pool);
    let config = service.set_plan_assignment_mode(mode, auth_user.id).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!({
        "message": "Plan assignment mode updated successfully",
        "config": config
    })))
}
