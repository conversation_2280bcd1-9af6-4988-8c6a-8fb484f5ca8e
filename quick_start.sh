#!/bin/bash

# MES系统快速启动脚本
# 用于快速设置开发环境和启动系统

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${PURPLE}🚀 MES系统快速启动${NC}"
    echo -e "${PURPLE}===================${NC}"
    echo ""
}

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查依赖
check_dependencies() {
    print_step "检查系统依赖..."
    
    # 检查Rust
    if ! command -v cargo &> /dev/null; then
        print_error "Rust未安装，请先安装Rust: https://rustup.rs/"
        exit 1
    fi
    print_success "Rust已安装: $(rustc --version)"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js未安装，请先安装Node.js: https://nodejs.org/"
        exit 1
    fi
    print_success "Node.js已安装: $(node --version)"
    
    # 检查PostgreSQL
    if ! command -v psql &> /dev/null; then
        print_warning "PostgreSQL客户端未找到，请确保PostgreSQL已安装并运行"
    else
        print_success "PostgreSQL客户端已安装"
    fi
}

# 设置环境变量
setup_environment() {
    print_step "设置环境变量..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "已创建.env文件（从.env.example复制）"
            print_warning "请检查并修改.env文件中的配置"
        else
            print_error ".env.example文件不存在"
            exit 1
        fi
    else
        print_success ".env文件已存在"
    fi
}

# 检查数据库连接
check_database() {
    print_step "检查数据库连接..."
    
    # 从.env文件读取数据库URL
    if [ -f ".env" ]; then
        source .env
    fi
    
    # 默认数据库URL
    DB_URL=${DATABASE_URL:-"postgresql://postgres:password@localhost:5432/mes-db"}
    
    # 简单的连接测试
    if command -v psql &> /dev/null; then
        if psql "$DB_URL" -c "SELECT 1;" &> /dev/null; then
            print_success "数据库连接正常"
        else
            print_warning "数据库连接失败，请检查PostgreSQL是否运行"
            print_warning "数据库URL: $DB_URL"
        fi
    else
        print_warning "无法测试数据库连接（psql未安装）"
    fi
}

# 构建后端
build_backend() {
    print_step "构建后端服务..."
    
    if cargo build; then
        print_success "后端构建成功"
    else
        print_error "后端构建失败"
        exit 1
    fi
}

# 安装前端依赖
setup_frontend() {
    print_step "设置前端环境..."
    
    cd frontend
    
    if [ ! -d "node_modules" ]; then
        print_step "安装前端依赖..."
        if npm install; then
            print_success "前端依赖安装成功"
        else
            print_error "前端依赖安装失败"
            exit 1
        fi
    else
        print_success "前端依赖已安装"
    fi
    
    cd ..
}

# 启动服务
start_services() {
    print_step "启动服务..."
    
    # 检查端口是否被占用
    if lsof -i :8080 &> /dev/null; then
        print_warning "端口8080已被占用，请先停止相关服务"
    fi
    
    if lsof -i :3000 &> /dev/null; then
        print_warning "端口3000已被占用，请先停止相关服务"
    fi
    
    echo ""
    echo -e "${PURPLE}🎯 启动选项:${NC}"
    echo "1. 启动后端服务 (端口8080)"
    echo "2. 启动前端服务 (端口3000)"
    echo "3. 同时启动前后端服务"
    echo "4. 运行API测试"
    echo "5. 退出"
    echo ""
    
    read -p "请选择 (1-5): " choice
    
    case $choice in
        1)
            print_step "启动后端服务..."
            cargo run
            ;;
        2)
            print_step "启动前端服务..."
            cd frontend && npm run dev
            ;;
        3)
            print_step "同时启动前后端服务..."
            print_warning "将在后台启动后端，前台启动前端"
            print_warning "按Ctrl+C停止前端，使用 pkill -f 'cargo run' 停止后端"
            
            # 后台启动后端
            cargo run &
            BACKEND_PID=$!
            
            # 等待后端启动
            sleep 5
            
            # 前台启动前端
            cd frontend
            npm run dev
            
            # 清理后端进程
            kill $BACKEND_PID 2>/dev/null || true
            ;;
        4)
            print_step "运行API测试..."
            if [ -f "test_api.sh" ]; then
                ./test_api.sh
            else
                print_error "test_api.sh文件不存在"
            fi
            ;;
        5)
            print_success "退出"
            exit 0
            ;;
        *)
            print_error "无效选择"
            exit 1
            ;;
    esac
}

# 显示访问信息
show_access_info() {
    print_step "访问信息"
    echo ""
    echo -e "${GREEN}🌐 访问地址:${NC}"
    echo "前端: http://localhost:3000"
    echo "后端API: http://localhost:8080"
    echo "API文档: http://localhost:8080/api"
    echo ""
    echo -e "${GREEN}👤 默认登录:${NC}"
    echo "用户名: admin"
    echo "密码: admin123"
    echo ""
    echo -e "${GREEN}📚 文档:${NC}"
    echo "API文档: API_DOCUMENTATION.md"
    echo "开发指南: FRONTEND_DEVELOPMENT_GUIDE.md"
    echo "项目总结: PROJECT_SUMMARY.md"
    echo ""
}

# 主函数
main() {
    print_header
    
    # 检查是否在项目根目录
    if [ ! -f "Cargo.toml" ] || [ ! -d "frontend" ]; then
        print_error "请在MES项目根目录运行此脚本"
        exit 1
    fi
    
    check_dependencies
    setup_environment
    check_database
    build_backend
    setup_frontend
    
    show_access_info
    start_services
}

# 运行主函数
main "$@"
