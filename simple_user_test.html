<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单用户技能组测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .warning { background-color: #fff3cd; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>简单用户技能组测试</h1>
    
    <button onclick="testUserSkills()">测试用户技能组</button>
    <div id="result"></div>

    <script>
        async function testUserSkills() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>正在测试...</p>';

            try {
                // 1. 登录
                const loginResponse = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                const loginData = await loginResponse.json();
                
                if (!loginResponse.ok) {
                    throw new Error('登录失败: ' + loginData.message);
                }

                const token = loginData.token;

                // 2. 获取用户数据
                const usersResponse = await fetch('http://localhost:8080/api/users', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const usersData = await usersResponse.json();
                
                if (!usersResponse.ok) {
                    throw new Error('获取用户数据失败: ' + usersData.message);
                }

                const users = usersData.users;

                // 3. 分析数据
                let html = '<div class="result success"><h3>测试结果</h3>';
                
                html += `<p><strong>总用户数:</strong> ${users.length}</p>`;
                
                // 检查每个用户的技能组
                const usersWithSkills = [];
                const usersWithoutSkills = [];
                
                users.forEach(user => {
                    const hasSkills = user.skills && user.skills.length > 0;
                    const skillsInfo = {
                        username: user.username,
                        fullName: user.full_name,
                        skills: user.skills,
                        skillsType: typeof user.skills,
                        skillsLength: user.skills ? user.skills.length : 'N/A',
                        hasSkills: hasSkills
                    };
                    
                    if (hasSkills) {
                        usersWithSkills.push(skillsInfo);
                    } else {
                        usersWithoutSkills.push(skillsInfo);
                    }
                });

                html += `<p><strong>有技能组用户:</strong> ${usersWithSkills.length}</p>`;
                html += `<p><strong>无技能组用户:</strong> ${usersWithoutSkills.length}</p>`;

                // 显示详细信息
                html += '<h4>用户详情:</h4>';
                users.forEach(user => {
                    const hasSkills = user.skills && user.skills.length > 0;
                    const bgColor = hasSkills ? '#d4edda' : '#f8d7da';
                    
                    html += `<div style="background-color: ${bgColor}; margin: 5px 0; padding: 5px; border-radius: 3px;">
                        <strong>${user.username}</strong> (${user.full_name || '未设置姓名'})<br>
                        技能组: ${JSON.stringify(user.skills)}<br>
                        类型: ${typeof user.skills}, 长度: ${user.skills ? user.skills.length : 'N/A'}<br>
                        有技能: ${hasSkills}
                    </div>`;
                });

                // 前端逻辑测试
                html += '<h4>前端逻辑测试:</h4>';
                const frontendFilter1 = users.filter(user => !user.skills || user.skills.length === 0);
                const frontendFilter2 = users.filter(user => user.skills && user.skills.length > 0);
                
                html += `<p>前端过滤器1 (!user.skills || user.skills.length === 0): ${frontendFilter1.length} 个用户</p>`;
                html += `<p>前端过滤器2 (user.skills && user.skills.length > 0): ${frontendFilter2.length} 个用户</p>`;
                
                if (frontendFilter1.length > 0) {
                    html += '<p style="color: red;"><strong>警告：前端认为有用户没有技能组！</strong></p>';
                    frontendFilter1.forEach(user => {
                        html += `<p style="color: red;">无技能用户: ${user.username} - ${JSON.stringify(user.skills)}</p>`;
                    });
                } else {
                    html += '<p style="color: green;"><strong>✓ 前端确认所有用户都有技能组</strong></p>';
                }

                html += '</div>';
                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="result error"><h3>错误</h3><p>${error.message}</p></div>`;
            }
        }
    </script>
</body>
</html>
