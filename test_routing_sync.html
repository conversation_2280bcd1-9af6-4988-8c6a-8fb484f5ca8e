<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺数据同步测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        button.success {
            background: #52c41a;
        }
        button.danger {
            background: #ff4d4f;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>工艺数据同步测试</h1>
        <p>此页面用于测试工艺创建后在生产计划中的数据同步问题</p>
        
        <div class="two-column">
            <div class="section">
                <h3>1. 创建测试工艺</h3>
                <div class="form-group">
                    <label>零件ID:</label>
                    <select id="partSelect">
                        <option value="">请选择零件</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>工序编号:</label>
                    <input type="number" id="stepNumber" value="1" min="1">
                </div>
                <div class="form-group">
                    <label>工艺名称:</label>
                    <input type="text" id="processName" value="测试工艺" placeholder="输入工艺名称">
                </div>
                <div class="form-group">
                    <label>工作指导:</label>
                    <input type="text" id="workInstructions" placeholder="输入工作指导（可选）">
                </div>
                <div class="form-group">
                    <label>标准工时:</label>
                    <input type="number" id="standardHours" step="0.1" placeholder="输入标准工时（可选）">
                </div>
                <button onclick="createTestRouting()" class="success">创建测试工艺</button>
                <div id="createResult" class="result"></div>
            </div>

            <div class="section">
                <h3>2. 验证数据同步</h3>
                <button onclick="checkRoutingInPlanTasks()">检查工艺在计划中的可见性</button>
                <button onclick="getAllRoutings()">获取所有工艺</button>
                <button onclick="simulatePlanTaskCreation()">模拟创建计划任务</button>
                <div id="syncResult" class="result"></div>
            </div>
        </div>

        <div class="section">
            <h3>3. 数据清理</h3>
            <button onclick="getLatestRoutings()" class="info">查看最新工艺</button>
            <button onclick="deleteTestRoutings()" class="danger">删除测试工艺</button>
            <div id="cleanupResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let authToken = null;
        let testRoutingIds = [];

        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    console.log('登录成功');
                    return true;
                } else {
                    console.error('登录失败');
                    return false;
                }
            } catch (error) {
                console.error('登录错误:', error);
                return false;
            }
        }

        async function apiRequest(endpoint, options = {}) {
            if (!authToken) {
                const loginSuccess = await login();
                if (!loginSuccess) {
                    throw new Error('登录失败');
                }
            }

            const response = await fetch(`${API_BASE}${endpoint}`, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.json();
        }

        function showResult(elementId, data, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }

        async function loadParts() {
            try {
                const data = await apiRequest('/parts');
                const select = document.getElementById('partSelect');
                select.innerHTML = '<option value="">请选择零件</option>';
                
                if (data.parts && data.parts.length > 0) {
                    data.parts.forEach(part => {
                        const option = document.createElement('option');
                        option.value = part.id;
                        option.textContent = `${part.part_number} - ${part.part_name || ''}`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载零件失败:', error);
            }
        }

        async function createTestRouting() {
            try {
                const partId = document.getElementById('partSelect').value;
                const stepNumber = document.getElementById('stepNumber').value;
                const processName = document.getElementById('processName').value;
                const workInstructions = document.getElementById('workInstructions').value;
                const standardHours = document.getElementById('standardHours').value;

                if (!partId || !stepNumber || !processName) {
                    showResult('createResult', '请填写必填字段：零件ID、工序编号、工艺名称', 'error');
                    return;
                }

                const requestData = {
                    part_id: parseInt(partId),
                    step_number: parseInt(stepNumber),
                    process_name: processName,
                    work_instructions: workInstructions || null,
                    standard_hours: standardHours ? parseFloat(standardHours) : null
                };

                const result = await apiRequest('/routings', {
                    method: 'POST',
                    body: JSON.stringify(requestData)
                });

                testRoutingIds.push(result.routing.id);
                showResult('createResult', `工艺创建成功！\nID: ${result.routing.id}\n数据: ${JSON.stringify(result.routing, null, 2)}`, 'success');
                
                // 自动增加工序编号
                document.getElementById('stepNumber').value = parseInt(stepNumber) + 1;
            } catch (error) {
                showResult('createResult', `创建失败: ${error.message}`, 'error');
            }
        }

        async function getAllRoutings() {
            try {
                const data = await apiRequest('/routings');
                showResult('syncResult', `获取到 ${data.routings.length} 个工艺:\n${JSON.stringify(data.routings, null, 2)}`, 'info');
            } catch (error) {
                showResult('syncResult', `获取工艺失败: ${error.message}`, 'error');
            }
        }

        async function checkRoutingInPlanTasks() {
            try {
                const [routingsData, planTasksData] = await Promise.all([
                    apiRequest('/routings'),
                    apiRequest('/plan-tasks')
                ]);

                const routings = routingsData.routings || [];
                const planTasks = planTasksData.plan_tasks || [];

                let report = '=== 工艺在计划任务中的可见性检查 ===\n\n';
                report += `总工艺数: ${routings.length}\n`;
                report += `总计划任务数: ${planTasks.length}\n\n`;

                if (testRoutingIds.length > 0) {
                    report += '测试工艺检查:\n';
                    testRoutingIds.forEach(id => {
                        const routing = routings.find(r => r.id === id);
                        if (routing) {
                            report += `✓ 工艺 ID ${id} 存在: ${routing.process_name}\n`;
                        } else {
                            report += `✗ 工艺 ID ${id} 不存在\n`;
                        }
                    });
                    report += '\n';
                }

                // 检查最新的几个工艺
                const latestRoutings = routings.slice(-5);
                report += '最新的5个工艺:\n';
                latestRoutings.forEach(r => {
                    report += `- ID:${r.id}, 零件:${r.part_number}, 工序:${r.step_number}, 工艺:${r.process_name}\n`;
                });

                showResult('syncResult', report, 'info');
            } catch (error) {
                showResult('syncResult', `检查失败: ${error.message}`, 'error');
            }
        }

        async function simulatePlanTaskCreation() {
            try {
                const [routingsData, workOrdersData] = await Promise.all([
                    apiRequest('/routings'),
                    apiRequest('/work-orders')
                ]);

                const routings = routingsData.routings || [];
                const workOrders = workOrdersData.work_orders || [];

                let report = '=== 模拟计划任务创建 ===\n\n';
                
                if (workOrders.length === 0) {
                    report += '没有可用的工单，无法创建计划任务\n';
                } else if (routings.length === 0) {
                    report += '没有可用的工艺，无法创建计划任务\n';
                } else {
                    const workOrder = workOrders[0];
                    const availableRoutings = routings.filter(r => r.part_id === workOrder.part_id);
                    
                    report += `选择工单: ${workOrder.work_order_number} (零件ID: ${workOrder.part_id})\n`;
                    report += `该零件的可用工艺: ${availableRoutings.length} 个\n\n`;
                    
                    if (availableRoutings.length > 0) {
                        report += '可选择的工艺步骤:\n';
                        availableRoutings.forEach(r => {
                            report += `- ID:${r.id}, 工序:${r.step_number}, 工艺:${r.process_name}\n`;
                        });
                    } else {
                        report += '该零件没有可用的工艺步骤，无法创建计划任务\n';
                    }
                }

                showResult('syncResult', report, 'info');
            } catch (error) {
                showResult('syncResult', `模拟失败: ${error.message}`, 'error');
            }
        }

        async function getLatestRoutings() {
            try {
                const data = await apiRequest('/routings');
                const routings = data.routings || [];
                const latest = routings.slice(-10);
                
                let report = `最新的 ${latest.length} 个工艺:\n\n`;
                latest.forEach(r => {
                    const isTest = testRoutingIds.includes(r.id) ? ' [测试]' : '';
                    report += `ID:${r.id}, 零件:${r.part_number}, 工序:${r.step_number}, 工艺:${r.process_name}${isTest}\n`;
                });

                showResult('cleanupResult', report, 'info');
            } catch (error) {
                showResult('cleanupResult', `获取失败: ${error.message}`, 'error');
            }
        }

        async function deleteTestRoutings() {
            if (testRoutingIds.length === 0) {
                showResult('cleanupResult', '没有测试工艺需要删除', 'info');
                return;
            }

            try {
                let report = '删除测试工艺:\n\n';
                for (const id of testRoutingIds) {
                    try {
                        await apiRequest(`/routings/${id}`, { method: 'DELETE' });
                        report += `✓ 删除工艺 ID ${id} 成功\n`;
                    } catch (error) {
                        report += `✗ 删除工艺 ID ${id} 失败: ${error.message}\n`;
                    }
                }
                
                testRoutingIds = [];
                showResult('cleanupResult', report, 'success');
            } catch (error) {
                showResult('cleanupResult', `删除失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时初始化
        window.onload = async function() {
            await login();
            await loadParts();
        };
    </script>
</body>
</html>
