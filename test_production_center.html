<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产执行中心测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #1890ff;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        .test-section h3 {
            color: #1890ff;
            margin-top: 0;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .test-link {
            display: block;
            padding: 12px 16px;
            background: #f0f2f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            text-decoration: none;
            color: #262626;
            transition: all 0.3s;
        }
        .test-link:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.new { background: #52c41a; color: white; }
        .status.old { background: #faad14; color: white; }
        .status.deprecated { background: #ff4d4f; color: white; }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #d9d9d9;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background: #fafafa;
            font-weight: bold;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:before {
            content: "✓";
            color: #52c41a;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 生产执行中心测试页面</h1>
            <p>测试新的统一生产执行中心功能，对比原有页面的改进</p>
        </div>

        <div class="test-section">
            <h3>📋 页面对比测试</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>页面</th>
                        <th>状态</th>
                        <th>目标用户</th>
                        <th>主要功能</th>
                        <th>测试链接</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>生产执行中心</strong></td>
                        <td><span class="status new">新版本</span></td>
                        <td>所有角色（根据权限显示不同功能）</td>
                        <td>统一的生产执行界面，角色自适应</td>
                        <td><a href="http://localhost:3000/production-center" target="_blank">测试新版本</a></td>
                    </tr>
                    <tr>
                        <td>执行跟踪</td>
                        <td><span class="status old">旧版本</span></td>
                        <td>管理员、工程师、计划员</td>
                        <td>任务跟踪、执行日志</td>
                        <td><a href="http://localhost:3000/execution" target="_blank">测试旧版本</a></td>
                    </tr>
                    <tr>
                        <td>操作员执行</td>
                        <td><span class="status old">旧版本</span></td>
                        <td>操作员</td>
                        <td>任务确认、设备状态、质量录入</td>
                        <td><a href="http://localhost:3000/operator-execution" target="_blank">测试旧版本</a></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🎯 新版本功能特性</h3>
            <ul class="feature-list">
                <li><strong>角色自适应界面</strong> - 根据用户角色动态显示功能模块</li>
                <li><strong>统一的用户体验</strong> - 消除了页面间的功能重复和混乱</li>
                <li><strong>操作员视图</strong> - 任务确认、设备状态、质量检查、完工确认</li>
                <li><strong>管理员视图</strong> - 执行跟踪、任务监控、执行日志</li>
                <li><strong>智能权限控制</strong> - 基于角色的功能访问控制</li>
                <li><strong>响应式设计</strong> - 适配不同屏幕尺寸和设备</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试场景</h3>
            <div class="test-links">
                <a href="http://localhost:3000/login" class="test-link" target="_blank">
                    <strong>1. 登录测试</strong><br>
                    使用不同角色账户登录测试权限
                </a>
                <a href="http://localhost:3000/production-center" class="test-link" target="_blank">
                    <strong>2. 操作员视图</strong><br>
                    测试任务确认、设备状态等功能
                </a>
                <a href="http://localhost:3000/production-center" class="test-link" target="_blank">
                    <strong>3. 管理员视图</strong><br>
                    测试执行跟踪、日志查看等功能
                </a>
                <a href="http://localhost:3000/dashboard" class="test-link" target="_blank">
                    <strong>4. 菜单导航</strong><br>
                    检查新菜单项是否正确显示
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试检查清单</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>测试项目</th>
                        <th>预期结果</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>页面加载</td>
                        <td>生产执行中心页面正常加载</td>
                        <td>⏳ 待测试</td>
                    </tr>
                    <tr>
                        <td>角色权限</td>
                        <td>不同角色看到不同的功能标签页</td>
                        <td>⏳ 待测试</td>
                    </tr>
                    <tr>
                        <td>任务列表</td>
                        <td>正确显示用户的任务列表</td>
                        <td>⏳ 待测试</td>
                    </tr>
                    <tr>
                        <td>任务操作</td>
                        <td>开始、暂停、完成任务功能正常</td>
                        <td>⏳ 待测试</td>
                    </tr>
                    <tr>
                        <td>执行日志</td>
                        <td>正确显示执行历史记录</td>
                        <td>⏳ 待测试</td>
                    </tr>
                    <tr>
                        <td>操作员功能</td>
                        <td>任务确认、设备状态、质量录入正常</td>
                        <td>⏳ 待测试</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🔧 问题反馈</h3>
            <p>如果在测试过程中发现问题，请记录以下信息：</p>
            <ul>
                <li>用户角色和权限</li>
                <li>操作步骤</li>
                <li>预期结果 vs 实际结果</li>
                <li>浏览器控制台错误信息</li>
                <li>网络请求状态</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📈 下一步计划</h3>
            <ul class="feature-list">
                <li><strong>功能验证</strong> - 确认所有功能模块正常工作</li>
                <li><strong>性能优化</strong> - 优化页面加载和响应速度</li>
                <li><strong>用户体验</strong> - 根据测试反馈改进界面设计</li>
                <li><strong>逐步迁移</strong> - 将用户从旧页面迁移到新页面</li>
                <li><strong>移除旧页面</strong> - 在确认新页面稳定后移除旧版本</li>
            </ul>
        </div>
    </div>

    <script>
        // 简单的测试状态跟踪
        document.addEventListener('DOMContentLoaded', function() {
            console.log('生产执行中心测试页面已加载');
            
            // 检查当前时间
            const now = new Date();
            console.log('测试时间:', now.toLocaleString('zh-CN'));
            
            // 检查前端服务状态
            fetch('http://localhost:3000/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('前端服务状态:', data);
                })
                .catch(error => {
                    console.error('前端服务检查失败:', error);
                });
        });
    </script>
</body>
</html>
