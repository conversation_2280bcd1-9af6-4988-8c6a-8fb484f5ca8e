<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺数据检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
        }
        .highlight {
            background-color: #fff2e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MES系统工艺数据检查</h1>
        
        <div class="section">
            <h3>1. 获取所有工艺数据</h3>
            <button onclick="getAllRoutings()">获取所有工艺</button>
            <button onclick="getRoutingsByPart()">按零件查询工艺</button>
            <div id="routingsResult" class="result"></div>
        </div>

        <div class="section">
            <h3>2. 获取所有零件数据</h3>
            <button onclick="getAllParts()">获取所有零件</button>
            <div id="partsResult" class="result"></div>
        </div>

        <div class="section">
            <h3>3. 获取所有工单数据</h3>
            <button onclick="getAllWorkOrders()">获取所有工单</button>
            <div id="workOrdersResult" class="result"></div>
        </div>

        <div class="section">
            <h3>4. 获取所有计划任务</h3>
            <button onclick="getAllPlanTasks()">获取所有计划任务</button>
            <div id="planTasksResult" class="result"></div>
        </div>

        <div class="section">
            <h3>5. 数据关联检查</h3>
            <button onclick="checkDataRelations()">检查数据关联</button>
            <div id="relationsResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        // 模拟登录获取token
        let authToken = null;
        
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    console.log('登录成功');
                    return true;
                } else {
                    console.error('登录失败');
                    return false;
                }
            } catch (error) {
                console.error('登录错误:', error);
                return false;
            }
        }

        async function apiRequest(endpoint, options = {}) {
            if (!authToken) {
                const loginSuccess = await login();
                if (!loginSuccess) {
                    throw new Error('登录失败');
                }
            }

            const response = await fetch(`${API_BASE}${endpoint}`, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.json();
        }

        function showResult(elementId, data, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }

        function showTable(elementId, data, columns) {
            const element = document.getElementById(elementId);
            element.className = 'result info';
            
            if (!data || data.length === 0) {
                element.innerHTML = '没有数据';
                return;
            }

            let html = '<table><thead><tr>';
            columns.forEach(col => {
                html += `<th>${col.title}</th>`;
            });
            html += '</tr></thead><tbody>';

            data.forEach(row => {
                html += '<tr>';
                columns.forEach(col => {
                    const value = col.key.split('.').reduce((obj, key) => obj?.[key], row);
                    html += `<td>${value || ''}</td>`;
                });
                html += '</tr>';
            });

            html += '</tbody></table>';
            element.innerHTML = html;
        }

        async function getAllRoutings() {
            try {
                const data = await apiRequest('/routings');
                console.log('工艺数据:', data);
                
                const columns = [
                    { title: 'ID', key: 'id' },
                    { title: '零件ID', key: 'part_id' },
                    { title: '零件编号', key: 'part_number' },
                    { title: '零件名称', key: 'part_name' },
                    { title: '工序编号', key: 'step_number' },
                    { title: '工艺名称', key: 'process_name' },
                    { title: '工作指导', key: 'work_instructions' },
                    { title: '标准工时', key: 'standard_hours' }
                ];
                
                showTable('routingsResult', data.routings, columns);
            } catch (error) {
                showResult('routingsResult', `错误: ${error.message}`, 'error');
            }
        }

        async function getRoutingsByPart() {
            try {
                // 先获取所有零件
                const partsData = await apiRequest('/parts');
                if (partsData.parts && partsData.parts.length > 0) {
                    const partId = partsData.parts[0].id;
                    const data = await apiRequest(`/parts/${partId}/routing`);
                    showResult('routingsResult', data, 'info');
                } else {
                    showResult('routingsResult', '没有找到零件数据', 'error');
                }
            } catch (error) {
                showResult('routingsResult', `错误: ${error.message}`, 'error');
            }
        }

        async function getAllParts() {
            try {
                const data = await apiRequest('/parts');
                console.log('零件数据:', data);
                
                const columns = [
                    { title: 'ID', key: 'id' },
                    { title: '零件编号', key: 'part_number' },
                    { title: '零件名称', key: 'part_name' },
                    { title: '版本', key: 'version' },
                    { title: '规格', key: 'specifications' }
                ];
                
                showTable('partsResult', data.parts, columns);
            } catch (error) {
                showResult('partsResult', `错误: ${error.message}`, 'error');
            }
        }

        async function getAllWorkOrders() {
            try {
                const data = await apiRequest('/work-orders');
                console.log('工单数据:', data);
                
                const columns = [
                    { title: 'ID', key: 'id' },
                    { title: '工单号', key: 'work_order_number' },
                    { title: '项目BOM ID', key: 'project_bom_id' },
                    { title: '零件ID', key: 'part_id' },
                    { title: '数量', key: 'quantity' },
                    { title: '状态', key: 'status' },
                    { title: '到期日期', key: 'due_date' }
                ];
                
                showTable('workOrdersResult', data.work_orders, columns);
            } catch (error) {
                showResult('workOrdersResult', `错误: ${error.message}`, 'error');
            }
        }

        async function getAllPlanTasks() {
            try {
                const data = await apiRequest('/plan-tasks');
                console.log('计划任务数据:', data);
                
                const columns = [
                    { title: 'ID', key: 'id' },
                    { title: '工单ID', key: 'work_order_id' },
                    { title: '工艺步骤ID', key: 'routing_step_id' },
                    { title: '技能组ID', key: 'skill_group_id' },
                    { title: '零件编号', key: 'part_number' },
                    { title: '工艺名称', key: 'process_name' },
                    { title: '状态', key: 'status' }
                ];
                
                showTable('planTasksResult', data.plan_tasks, columns);
            } catch (error) {
                showResult('planTasksResult', `错误: ${error.message}`, 'error');
            }
        }

        async function checkDataRelations() {
            try {
                const [routingsData, partsData, workOrdersData, planTasksData] = await Promise.all([
                    apiRequest('/routings'),
                    apiRequest('/parts'),
                    apiRequest('/work-orders'),
                    apiRequest('/plan-tasks')
                ]);

                const routings = routingsData.routings || [];
                const parts = partsData.parts || [];
                const workOrders = workOrdersData.work_orders || [];
                const planTasks = planTasksData.plan_tasks || [];

                let report = '=== 数据关联检查报告 ===\n\n';
                
                report += `总计数据:\n`;
                report += `- 零件: ${parts.length} 个\n`;
                report += `- 工艺: ${routings.length} 个\n`;
                report += `- 工单: ${workOrders.length} 个\n`;
                report += `- 计划任务: ${planTasks.length} 个\n\n`;

                // 检查哪些零件有工艺
                const partsWithRoutings = new Set(routings.map(r => r.part_id));
                const partsWithoutRoutings = parts.filter(p => !partsWithRoutings.has(p.id));
                
                report += `零件工艺关联:\n`;
                report += `- 有工艺的零件: ${partsWithRoutings.size} 个\n`;
                report += `- 没有工艺的零件: ${partsWithoutRoutings.length} 个\n`;
                if (partsWithoutRoutings.length > 0) {
                    report += `  没有工艺的零件: ${partsWithoutRoutings.map(p => `${p.part_number}(ID:${p.id})`).join(', ')}\n`;
                }
                report += '\n';

                // 检查工单和零件的关联
                const workOrderPartIds = new Set(workOrders.map(wo => wo.part_id).filter(id => id));
                report += `工单零件关联:\n`;
                report += `- 工单关联的零件数: ${workOrderPartIds.size} 个\n`;
                
                // 检查计划任务中引用的工艺步骤
                const planTaskRoutingIds = new Set(planTasks.map(pt => pt.routing_step_id));
                const routingIds = new Set(routings.map(r => r.id));
                const missingRoutings = [...planTaskRoutingIds].filter(id => !routingIds.has(id));
                
                report += `计划任务工艺关联:\n`;
                report += `- 计划任务引用的工艺步骤: ${planTaskRoutingIds.size} 个\n`;
                report += `- 实际存在的工艺步骤: ${routingIds.size} 个\n`;
                if (missingRoutings.length > 0) {
                    report += `- 缺失的工艺步骤ID: ${missingRoutings.join(', ')}\n`;
                }
                report += '\n';

                // 最新的工艺数据
                if (routings.length > 0) {
                    const latestRoutings = routings.slice(-5);
                    report += `最新的5个工艺步骤:\n`;
                    latestRoutings.forEach(r => {
                        report += `- ID:${r.id}, 零件:${r.part_number}, 工序:${r.step_number}, 工艺:${r.process_name}\n`;
                    });
                }

                showResult('relationsResult', report, 'info');
            } catch (error) {
                showResult('relationsResult', `错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动登录
        window.onload = function() {
            login();
        };
    </script>
</body>
</html>
